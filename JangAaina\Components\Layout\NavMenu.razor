﻿@using Color = Microsoft.FluentUI.AspNetCore.Components.Color
@using Icons = Microsoft.FluentUI.AspNetCore.Components.Icons
@rendermode InteractiveServer

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu" onclick="document.getElementById('navmenu-toggle').click();">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded">
            <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>
            @*<FluentNavLink Href="/setup/clients" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size24.Collections())" IconColor="Color.Accent">Clients</FluentNavLink>*@
            <FluentNavLink Href="/setup/ratecards" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.CurrencyDollarRupee())" IconColor="Color.Accent">Rate Cards</FluentNavLink>
            <FluentNavLink Href="/setup/focratecards" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.CurrencyDollarEuro())" IconColor="Color.Accent">FOC Rate Cards</FluentNavLink>
            <FluentNavLink Href="/setup/import-excel" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.DocumentTable())" IconColor="Color.Accent">Import Excel</FluentNavLink>
            @*<FluentNavLink Href="/setup/ratecards/apply" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Money())" IconColor="Color.Accent">Apply Rate Cards</FluentNavLink>*@
            @*<FluentNavLink Href="/setup/ratecards/import" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Check())" IconColor="Color.Accent">Import Data</FluentNavLink>*@
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
}
