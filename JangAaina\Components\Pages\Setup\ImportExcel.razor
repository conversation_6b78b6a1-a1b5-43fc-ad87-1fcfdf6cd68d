@page "/setup/import-excel"
@using EFCore.BulkExtensions
@using JangAaina.Models
@using Microsoft.EntityFrameworkCore
@using OfficeOpenXml
@using System.IO
@using System.Threading.Tasks
@using System.Linq
@using System.Collections.Generic
@using System.ComponentModel.DataAnnotations
@using System.Globalization
@rendermode InteractiveServer

@inject ApplicationDbcontext DbContext
@inject Microsoft.FluentUI.AspNetCore.Components.IDialogService DialogService
<SfToast @ref="ToastObj"></SfToast>
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h3>Import Excel Data</h3>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                @* <div class="col-12">
                    <div class="border rounded p-4 text-center" style="border-style: dashed !important; cursor:pointer">
                        <Microsoft.AspNetCore.Components.Forms.InputFile id="fileInput" OnChange="OnFileSelected" class="d-none" />
                        <label for="fileInput" class="btn btn-primary">
                            <i class="bi bi-cloud-upload"></i> Select Excel File
                        </label>
                        <span class="ms-2">@(fileName ?? "No file selected")</span>
                    </div>
                </div> *@
                <div style="width: 100%;">
    <div style="border: 2px dashed #dee2e6; border-radius: 0.375rem; padding: 1.5rem; text-align: center; cursor: pointer;">
        <Microsoft.AspNetCore.Components.Forms.InputFile id="fileInput" OnChange="OnFileSelected" style="display: none;" />
        <label for="fileInput" style="background-color: #0d6efd; color: white; border: 1px solid #0d6efd; padding: 0.375rem 0.75rem; font-size: 1rem; border-radius: 0.375rem; cursor: pointer; display: inline-block; text-decoration: none;">
            <i class="bi bi-cloud-upload"></i> Select Excel File
        </label>
        <span style="margin-left: 0.5rem;">@(fileName ?? "No file selected")</span>
    </div>
</div>
            </div>

            @if (excelData != null && excelData.Any())
            {
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="row">
                            <div class="col-md">
                                <h4>Preview Data</h4>
                            </div>
                            <div class="col-md text-end">
                                <SfButton Cssclass="e-primary" OnClick="ImportData">
                                     Import Data
                                </SfButton>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <SfGrid DataSource="@excelData"
                                    AllowPaging="true"
                                    Height="calc(100vh - 400px)"
                                    Width="100%"
                                    AllowSorting="true"
                                    AllowFiltering="true">
                                <GridPageSettings PageSize="10" PageSizes="new int[]{ 5, 10, 20, 50, 100 }"></GridPageSettings>
                                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel" />
                                <GridColumns>
                                    <GridColumn Field="@nameof(RawDatum.Date)" HeaderText="Date" Format="yyyy-MM-dd" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Day)" HeaderText="Day" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Country)" HeaderText="Country" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.City)" HeaderText="City" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Publication)" HeaderText="Publication" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.SubPublication)" HeaderText="SubPublication" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Language)" HeaderText="Language" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Industry)" HeaderText="Industry" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Category)" HeaderText="Category" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Brand)" HeaderText="Brand" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Caption)" HeaderText="Caption" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Company)" HeaderText="Company" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.PageType)" HeaderText="PageType" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.AdType)" HeaderText="AdType" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Colour)" HeaderText="Colour" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.PageNo)" HeaderText="PageNo" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Size1)" HeaderText="Size1" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Size2)" HeaderText="Size2" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Space)" HeaderText="Space" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Cost)" HeaderText="Cost" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.MediaAgency)" HeaderText="MediaAgency" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.PublishType)" HeaderText="PublishType" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Frequency)" HeaderText="Frequency" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.GovCategory)" HeaderText="GovCategory" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.GovAdType)" HeaderText="GovAdType" AutoFit="true"></GridColumn>
                                    <GridColumn Field="@nameof(RawDatum.Edition)" HeaderText="Edition" AutoFit="true"></GridColumn>
                                </GridColumns>
                            </SfGrid>
                        </div>
                    </div>
                </div>

                
            }
        </div>
    </div>
    <div class="card">
        <div class="card-header">
            <h3><a href="/setup/ratecards" target="_blank" style="text-decoration: none; color: inherit;">Rate Cards</a></h3>
        </div>
        <div class="card-body">
            <p>Click the title to open Rate Cards in a new window.</p>
        </div>
    </div>
</div>

@if (isProcessing)
{
    <div class="fullscreen-overlay">
        <div class="spinner-container">
            <div class="custom-spinner"></div>
            <div class="loading-text">Processing...</div>
        </div>
    </div>
}

<style>
    .fullscreen-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .spinner-container {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .custom-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }

    .loading-text {
        color: white;
        margin-top: 15px;
        font-size: 18px;
    }

    @@keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>

@code {
    private string fileName;
    private List<RawDatum> excelData = new List<RawDatum>();
    private bool isProcessing = false;
    private SfToast? ToastObj;
    private async Task OnFileSelected(InputFileChangeEventArgs e)
    {
        try
        {
            isProcessing = true;
            fileName = e.File.Name;
            // set non commercial use of epplus 
            //ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            ExcelPackage.License.SetNonCommercialPersonal("jawaid");

            // Read the file
            using var stream = e.File.OpenReadStream(maxAllowedSize: 10485760); // 10MB max
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);
            memoryStream.Position = 0;
            using var package = new ExcelPackage(memoryStream);
            var worksheet = package.Workbook.Worksheets[0]; // Get first worksheet

            // Clear existing data
            excelData.Clear();

            // Get the number of rows and columns
            int rowCount = worksheet.Dimension.Rows;
            int colCount = worksheet.Dimension.Columns;

            // Skip header row
            for (int row = 2; row <= rowCount; row++)
            {
                var rawDatum = new RawDatum();

                rawDatum.Id = Guid.NewGuid();
                rawDatum.Date = DateOnly.Parse(worksheet.Cells[row, 1].Text);
                rawDatum.Day = worksheet.Cells[row, 2].Text;

                rawDatum.Country = worksheet.Cells[row, 5].Text;
                rawDatum.City = worksheet.Cells[row, 6].Text;
                rawDatum.Publication = worksheet.Cells[row, 7].Text;
                rawDatum.SubPublication = worksheet.Cells[row, 8].Text;
                rawDatum.Language = worksheet.Cells[row, 9].Text;
                rawDatum.Industry = worksheet.Cells[row, 10].Text;
                rawDatum.Category = worksheet.Cells[row, 11].Text;
                rawDatum.Brand = worksheet.Cells[row, 12].Text;
                rawDatum.Variant = worksheet.Cells[row, 13].Text;
                rawDatum.Caption = worksheet.Cells[row, 14].Text;
                rawDatum.Company = worksheet.Cells[row, 15].Text;
                rawDatum.PageType = worksheet.Cells[row, 16].Text;
                rawDatum.AdType = worksheet.Cells[row, 17].Text;
                rawDatum.Colour = worksheet.Cells[row, 18].Text;
                rawDatum.PageNo = ParseInt(worksheet.Cells[row, 19].Text);
                rawDatum.Size1 = ParseInt(worksheet.Cells[row, 20].Text);
                rawDatum.Size2 = ParseInt(worksheet.Cells[row, 21].Text);
                rawDatum.Space = ParseDouble(worksheet.Cells[row, 22].Text);
                rawDatum.Cost = ParseDouble(worksheet.Cells[row, 23].Text);
                rawDatum.MediaAgency = worksheet.Cells[row, 24].Text;
                rawDatum.PublishType = worksheet.Cells[row, 25].Text;
                rawDatum.Frequency = worksheet.Cells[row, 26].Text;
                rawDatum.GovCategory = worksheet.Cells[row, 27].Text;
                rawDatum.GovAdType = worksheet.Cells[row, 28].Text;

                excelData.Add(rawDatum);
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            await ShowError($"Error reading Excel file: {ex.Message}");
        }
        finally
        {
            isProcessing = false;
        }
    }

    private DateOnly? ParseDate(string value)
    {
        DateOnly? dd = null;
        try
        {
            dd = DateOnly.Parse(value);
        }
        catch (Exception)
        {


        }
        return dd;

    }
    private DateOnly? ParseDate2(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return null;

        // First try to parse as regular date formats
        string[] formats = { "dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd", "dd-MM-yyyy", "MM-dd-yyyy" };
        foreach (var format in formats)
        {
            if (DateOnly.TryParseExact(value, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateOnly result))
                return result;
        }

        // If regular parsing fails, try to parse as Excel number
        if (double.TryParse(value, out double excelDate))
        {
            try
            {
                // Excel's date system has a base date of January 1, 1900
                // and counts the number of days since then
                DateTime baseDate = new DateTime(1900, 1, 1);
                // Subtract 2 to account for Excel's leap year bug
                DateTime dateValue = baseDate.AddDays(excelDate - 2);
                return DateOnly.FromDateTime(dateValue);
            }
            catch
            {
                // If conversion fails, return null
                return null;
            }
        }

        return null;
    }

    private int? ParseInt(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return null;

        if (int.TryParse(value, out int result))
            return result;

        return null;
    }

    private double? ParseDouble(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return null;

        if (double.TryParse(value, out double result))
            return result;

        return null;
    }

    private async Task ImportData()
    {
        try
        {
            isProcessing = true;

            // Get the date range from the Excel data
            var minDate = excelData.Min(x => x.Date);
            var maxDate = excelData.Max(x => x.Date);

            // Bulk delete existing data for the date range
            // await DbContext.RawData.ExecuteDeleteAsync(x => x.Date >= minDate && x.Date <= maxDate);
            var rawDataRange = DbContext.RawData.Where(x => x.Date >= minDate && x.Date <= maxDate);
            DbContext.RawData.RemoveRange(rawDataRange);
            await DbContext.SaveChangesAsync();
            //await DbContext.SaveChangesAsync();

            // Bulk insert new data
            await DbContext.BulkInsertAsync(excelData);

            // Clear the grid and show success message
            excelData.Clear();
            fileName = null;
            StateHasChanged();

            await ShowSuccess("Data imported successfully!");
        }
        catch (Exception ex)
        {
            await ShowError($"Error importing data: {ex.Message}");
        }
        finally
        {
            isProcessing = false;
        }
    }

    private async Task ShowSuccess(string message)
    {
        //var parameters = new Microsoft.FluentUI.AspNetCore.Components.DialogParameters<MessageBoxContent>
        //{
        //    { "Title", "Success" },
        //    { "Content", message },
        //    { "ButtonText", "OK" }
        //};

        //await DialogService.ShowMessageBoxAsync(parameters);
        //await DialogService.ShowMessageBoxAsync(parameters);
        var tm = new ToastModel
        {
            Title = "success",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 4000,
            Content = message
        };
        if (ToastObj != null)
        {
            await ToastObj.ShowAsync(tm);
        }
    }

    private async Task ShowError(string message)
    {
        //var parameters = new Microsoft.FluentUI.AspNetCore.Components.DialogParameters<MessageBoxContent>
        //{
        //    { "Title", "Error" },
        //    { "Content", message },
        //    { "ButtonText", "OK" }
        //};

        //await DialogService.ShowMessageBoxAsync(parameters);
        var tm = new ToastModel
        {
            Title = "Error",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 4000,
            Content = message
        };
        if(ToastObj!=null)
        {
            await ToastObj.ShowAsync(tm);
        }
    }
}














